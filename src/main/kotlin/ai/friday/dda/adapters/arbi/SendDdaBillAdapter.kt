package ai.friday.dda.adapters.arbi

import ai.friday.dda.adapters.jobs.TenantConfiguration
import ai.friday.dda.and
import ai.friday.dda.markers
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.HttpVersion
import io.micronaut.http.client.HttpClient
import io.micronaut.http.client.HttpClientRegistry
import io.micronaut.http.client.exceptions.HttpClientResponseException
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class SendDdaBillAdapter(
    private val cli: HttpClientRegistry<HttpClient>,
    private val config: Map<String, TenantConfiguration>,
) {
    private val logger = LoggerFactory.getLogger(SendDdaBillAdapter::class.java)

    fun send(list: List<DDABillTO>, tenantName: TenantName): Result<Boolean> {
        val client = cli.getClient(HttpVersion.HTTP_1_1, tenantName.name, null)
        val configuration =
            config[tenantName.name.lowercase()]?.configuration as? ResponseConfiguration.HttpConfiguration

        if (client == null || configuration == null) {
            throw IllegalStateException("Client not found for tenant $tenantName")
        }

        return send(list, configuration, client)
    }

    fun sendError(response: String, tenantName: TenantName): Result<Boolean> {
        val client = cli.getClient(HttpVersion.HTTP_1_1, tenantName.name, null)
        val configuration =
            config[tenantName.name.lowercase()]?.configuration as? ResponseConfiguration.HttpConfiguration

        if (client == null || configuration == null) {
            throw IllegalStateException("Client not found for tenant $tenantName")
        }

        return send(response, configuration, client)
    }

    private fun send(
        request: String,
        configuration: ResponseConfiguration.HttpConfiguration,
        client: HttpClient,
    ): Result<Boolean> {
        val httpRequest = HttpRequest.POST("/dda", request)
            .basicAuth(configuration.username, configuration.password)

        val response = client.toBlocking().exchange(
            httpRequest,
            Argument.STRING,
            Argument.STRING
        )

        try {
            val response = client.toBlocking().exchange(
                httpRequest,
                Argument.STRING,
                Argument.STRING,
            )

            return Result.success(response.status == HttpStatus.OK)
        } catch (e: HttpClientResponseException) {
            logger.error(e.markers().and("request" to list), "SettlementClient#sendResponse")
            return Result.failure(e)
        } catch (e: Exception) {
            logger.error(Markers.append("request", list), "SettlementClient#sendResponse", e)
            return Result.failure(e)
        }
    }
}